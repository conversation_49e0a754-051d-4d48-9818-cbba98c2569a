/**
 * 肩部触摸动作检测器
 * 实现分阶段检测和评分逻辑
 */
import { KeyPointMapping } from '@/utils/poseConstants'
import { useActionDetectionEngine } from '../useActionDetectionEngine'

export function useShoulderTouchDetector() {
  const engine = useActionDetectionEngine()

  // 动作所需关键点配置
  const REQUIRED_KEYPOINTS = {
    left: [KeyPointMapping.LEFT_WRIST, KeyPointMapping.RIGHT_SHOULDER, KeyPointMapping.LEFT_SHOULDER],
    right: [KeyPointMapping.RIGHT_WRIST, KeyPointMapping.LEFT_SHOULDER, KeyPointMapping.RIGHT_SHOULDER]
  }

  // 难度等级配置
  const DIFFICULTY_CONFIG = {
    easy: {
      touchThreshold: 0.25,      // 触摸距离阈值（相对肩宽）
      approachThreshold: 0.5,    // 接近距离阈值
      perfectThreshold: 0.15,    // 完美触摸阈值
      minScore: 60,              // 最低得分
      maxScore: 100              // 最高得分
    },
    medium: {
      touchThreshold: 0.2,
      approachThreshold: 0.4,
      perfectThreshold: 0.12,
      minScore: 70,
      maxScore: 100
    },
    hard: {
      touchThreshold: 0.15,
      approachThreshold: 0.3,
      perfectThreshold: 0.1,
      minScore: 80,
      maxScore: 100
    }
  }

  /**
   * 检测肩部触摸动作
   * @param {Array} keypoints - 关键点数组（画布坐标）
   * @param {string} side - 动作侧面 ('left' | 'right')
   * @param {string} difficultyLevel - 难度等级
   * @param {number} canvasWidth - 画布宽度
   * @param {number} canvasHeight - 画布高度
   * @returns {Object} - 检测结果
   */
  const detectShoulderTouch = (keypoints, side = 'left', difficultyLevel = 'easy', canvasWidth = 640, canvasHeight = 480) => {
    const config = DIFFICULTY_CONFIG[difficultyLevel]
    const requiredPoints = REQUIRED_KEYPOINTS[side]

    // 检查关键点完整性（使用画布坐标）
    const completenessCheck = engine.checkRequiredKeypoints(keypoints, requiredPoints, side, canvasWidth, canvasHeight, false)
    if (!completenessCheck.isComplete) {
      return {
        accuracy: 0,
        stage: 'incomplete',
        feedback: completenessCheck.message,
        distance: Infinity,
        normalizedDistance: Infinity
      }
    }

    // 获取关键点
    const activeWrist = side === 'left' ? 
      keypoints[KeyPointMapping.LEFT_WRIST] : 
      keypoints[KeyPointMapping.RIGHT_WRIST]
    
    const targetShoulder = side === 'left' ? 
      keypoints[KeyPointMapping.RIGHT_SHOULDER] : 
      keypoints[KeyPointMapping.LEFT_SHOULDER]

    // 计算距离（使用画布坐标）
    const distance = engine.calculateDistance(activeWrist, targetShoulder, false)
    const normalizedDistance = engine.normalizeDistance(distance, keypoints, false)

    if (normalizedDistance === Infinity) {
      return {
        accuracy: 0,
        stage: 'invalid',
        feedback: '无法计算动作距离',
        distance: Infinity,
        normalizedDistance: Infinity
      }
    }

    // 判断动作阶段和计算得分
    const result = calculateShoulderTouchScore(normalizedDistance, config, side)
    
    return {
      ...result,
      distance,
      normalizedDistance
    }
  }

  /**
   * 计算肩部触摸得分和阶段
   * @param {number} normalizedDistance - 标准化距离
   * @param {Object} config - 难度配置
   * @param {string} side - 动作侧面
   * @returns {Object} - 得分和反馈
   */
  const calculateShoulderTouchScore = (normalizedDistance, config, side) => {
    const sideText = side === 'left' ? '左手' : '右手'
    const targetText = side === 'left' ? '右肩' : '左肩'

    // 完美触摸
    if (normalizedDistance <= config.perfectThreshold) {
      return {
        accuracy: config.maxScore,
        stage: 'perfect_touch',
        feedback: `${sideText}完美触摸${targetText}！`
      }
    }

    // 良好触摸
    if (normalizedDistance <= config.touchThreshold) {
      const score = Math.round(
        config.maxScore - ((normalizedDistance - config.perfectThreshold) / 
        (config.touchThreshold - config.perfectThreshold)) * 20
      )
      return {
        accuracy: Math.max(config.minScore + 20, score),
        stage: 'good_touch',
        feedback: `${sideText}触摸${targetText}很好！`
      }
    }

    // 接近阶段
    if (normalizedDistance <= config.approachThreshold) {
      const score = Math.round(
        config.minScore + ((config.approachThreshold - normalizedDistance) / 
        (config.approachThreshold - config.touchThreshold)) * 20
      )
      return {
        accuracy: Math.max(config.minScore, score),
        stage: 'approaching',
        feedback: `${sideText}正在接近${targetText}，继续！`
      }
    }

    // 准备阶段
    if (normalizedDistance <= 1.0) {
      const score = Math.round(
        (1.0 - normalizedDistance) / (1.0 - config.approachThreshold) * config.minScore
      )
      return {
        accuracy: Math.max(0, score),
        stage: 'preparing',
        feedback: `请将${sideText}向${targetText}移动`
      }
    }

    // 距离太远
    return {
      accuracy: 0,
      stage: 'too_far',
      feedback: `请将${sideText}靠近${targetText}`
    }
  }

  /**
   * 检查动作是否完成
   * @param {Object} detectionResult - 检测结果
   * @param {string} difficultyLevel - 难度等级
   * @returns {boolean} - 是否完成
   */
  const isActionCompleted = (detectionResult, difficultyLevel = 'easy') => {
    const config = DIFFICULTY_CONFIG[difficultyLevel]
    return detectionResult.accuracy >= config.minScore + 20 && 
           ['good_touch', 'perfect_touch'].includes(detectionResult.stage)
  }

  /**
   * 获取动作指导建议
   * @param {Object} detectionResult - 检测结果
   * @param {string} side - 动作侧面
   * @returns {string} - 指导建议
   */
  const getGuidance = (detectionResult, side) => {
    const sideText = side === 'left' ? '左手' : '右手'
    const targetText = side === 'left' ? '右肩' : '左肩'

    switch (detectionResult.stage) {
      case 'incomplete':
        return detectionResult.feedback
      case 'too_far':
        return `抬起${sideText}，慢慢向${targetText}移动`
      case 'preparing':
        return `继续将${sideText}向${targetText}靠近`
      case 'approaching':
        return `很好！${sideText}快要触摸到${targetText}了`
      case 'good_touch':
        return `保持${sideText}触摸${targetText}的姿势`
      case 'perfect_touch':
        return `完美！保持这个姿势`
      default:
        return '请按照示范动作进行'
    }
  }

  return {
    // 主要检测方法
    detectShoulderTouch,
    isActionCompleted,
    getGuidance,
    
    // 配置
    REQUIRED_KEYPOINTS,
    DIFFICULTY_CONFIG
  }
}
