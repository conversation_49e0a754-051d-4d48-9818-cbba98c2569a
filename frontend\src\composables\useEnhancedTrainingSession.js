/**
 * 增强版训练会话管理
 * 整合模块化动作检测、智能反馈和声音提示
 */
import { ref, computed, watch } from 'vue'
import { useActionDetectionEngine } from './useActionDetectionEngine'
import { useShoulderTouchDetector } from './detectors/shoulderTouchDetector'
import { useArmRaiseDetector } from './detectors/armRaiseDetector'
import { useFingerTouchDetector } from './detectors/fingerTouchDetector'
import { usePalmFlipDetector } from './detectors/palmFlipDetector'
import { useActionFeedback } from './useActionFeedback'
import { useActionScoring } from './useActionScoring'
import { useAudioFeedback } from './useAudioFeedback'
import { useStateTransition } from './useStateTransition'
import { useTrainingStore } from '@/stores/training'
import { useConnectionStore } from '@/stores/connection'
import { useNotificationStore } from '@/stores/notification'

export function useEnhancedTrainingSession() {
  // 核心检测引擎
  const detectionEngine = useActionDetectionEngine()

  // 动作检测器
  const shoulderTouchDetector = useShoulderTouchDetector()
  const armRaiseDetector = useArmRaiseDetector()
  const fingerTouchDetector = useFingerTouchDetector()
  const palmFlipDetector = usePalmFlipDetector()

  // 反馈和评分系统
  const actionFeedback = useActionFeedback()
  const actionScoring = useActionScoring()
  const audioFeedback = useAudioFeedback()
  const stateTransition = useStateTransition()

  // 状态管理
  const trainingStore = useTrainingStore()
  const connectionStore = useConnectionStore()
  const notificationStore = useNotificationStore()

  // 训练会话状态
  const isTrainingActive = ref(false)
  const actionStartTime = ref(null)
  const actionHoldTime = ref(0)
  const actionHoldTimer = ref(null)
  const thresholdReached = ref(false)
  const detectionInterval = ref(null)

  // 准备阶段状态
  const isReadyForAction = ref(false)
  const readyFrameCount = ref(0)
  const showReadyIndicator = ref(false)
  const READY_FRAME_THRESHOLD = 15 // 需要连续15帧完整展示

  // 检测配置
  const DETECTION_CONFIG = {
    sampleInterval: 200, // 检测间隔（毫秒）
    scoreThreshold: 85, // 动作完成阈值
    holdDuration: 2000, // 保持时间（毫秒）
    feedbackInterval: 3000 // 反馈间隔（毫秒）
  }

  // 计算属性
  const currentActionType = computed(() => trainingStore.currentAction?.action_type)
  const currentActionSide = computed(() => trainingStore.currentAction?.side || 'left')
  const currentDifficultyLevel = computed(() => trainingStore.currentAction?.difficulty_level || 'easy')
  
  const isActionInProgress = computed(() => isTrainingActive.value && !!currentActionType.value)
  
  const actionCompletionProgress = computed(() => {
    if (!isActionInProgress.value) return 0
    return Math.min(100, (actionHoldTime.value / DETECTION_CONFIG.holdDuration) * 100)
  })

  const currentScores = computed(() => actionScoring.currentScores.value)
  const visibilityStatus = computed(() => detectionEngine.visibilityStatus.value)
  const currentFeedback = computed(() => actionFeedback.currentFeedback.value)

  /**
   * 开始训练会话
   */
  const startTrainingSession = () => {
    if (isTrainingActive.value) return

    console.log('[EnhancedTrainingSession] 开始增强训练会话 - 已修复actionDetection引用问题')
    console.log('[EnhancedTrainingSession] 当前动作类型:', currentActionType.value)
    console.log('[EnhancedTrainingSession] 当前动作侧面:', currentActionSide.value)
    console.log('[EnhancedTrainingSession] 当前难度等级:', currentDifficultyLevel.value)
    console.log('[EnhancedTrainingSession] 当前分数状态:', actionScoring.currentScores.value)

    isTrainingActive.value = true

    // 重置状态
    resetActionState()

    // 开始实时检测
    startRealtimeDetection()
  }

  /**
   * 停止训练会话
   */
  const stopTrainingSession = () => {
    if (!isTrainingActive.value) return

    console.log('[EnhancedTrainingSession] 停止增强训练会话')
    isTrainingActive.value = false

    // 停止检测间隔
    if (detectionInterval.value) {
      clearInterval(detectionInterval.value)
      detectionInterval.value = null
    }

    // 停止所有音频
    audioFeedback.stopAllAudio()

    // 重置状态
    resetActionState()
  }

  /**
   * 开始实时动作检测
   */
  const startRealtimeDetection = () => {
    console.log('[EnhancedTrainingSession] 启动实时检测')
    console.log('[EnhancedTrainingSession] 检测配置:', DETECTION_CONFIG)

    detectionInterval.value = setInterval(() => {
      if (!isTrainingActive.value || !currentActionType.value) {
        console.log('[EnhancedTrainingSession] 检测停止条件触发:', {
          isTrainingActive: isTrainingActive.value,
          currentActionType: currentActionType.value
        })
        clearInterval(detectionInterval.value)
        detectionInterval.value = null
        return
      }

      // 获取转换后的关键点坐标
      const transformedKeypoints = connectionStore.transformedKeypoints
      const transformParams = connectionStore.transformParams

      if (!transformedKeypoints || transformedKeypoints.length === 0) {
        console.log('[EnhancedTrainingSession] 没有转换后关键点数据，连接状态:', connectionStore.isConnected)
        return
      }

      if (!transformParams || !transformParams.canvasWidth || !transformParams.canvasHeight) {
        console.log('[EnhancedTrainingSession] 转换参数不完整，跳过检测', transformParams)
        return
      }

      // 验证转换后坐标的有效性
      const validKeypoints = transformedKeypoints.filter(kp => kp && typeof kp.x === 'number' && typeof kp.y === 'number')
      if (validKeypoints.length === 0) {
        console.log('[EnhancedTrainingSession] 转换后坐标无效，跳过检测')
        return
      }

      console.log('[EnhancedTrainingSession] 检测到转换后关键点数据，数量:', transformedKeypoints.length)

      // 执行模块化检测 - 使用转换后的画布坐标
      const detectionResult = performActionDetection(
        currentActionType.value,
        transformedKeypoints,
        currentActionSide.value,
        currentDifficultyLevel.value,
        transformParams.canvasWidth,
        transformParams.canvasHeight
      )

      // 处理检测结果
      handleDetectionResult(detectionResult)

    }, DETECTION_CONFIG.sampleInterval)
  }

  /**
   * 执行动作检测（使用模块化检测器）
   * @param {string} actionType - 动作类型
   * @param {Array} keypoints - 转换后的关键点数组（画布坐标）
   * @param {string} side - 动作侧面
   * @param {string} difficultyLevel - 难度等级
   * @param {number} canvasWidth - 画布宽度
   * @param {number} canvasHeight - 画布高度
   * @returns {Object} - 检测结果
   */
  const performActionDetection = (actionType, keypoints, side, difficultyLevel, canvasWidth, canvasHeight) => {
    let detectionResult = null

    // 首先检查画面完整性（使用画布坐标）
    const requiredKeypoints = getRequiredKeypoints(actionType, side)
    const completenessCheck = detectionEngine.checkRequiredKeypoints(keypoints, requiredKeypoints, side, canvasWidth, canvasHeight, false)

    // 如果画面不完整或还未准备就绪，返回零分
    if (!completenessCheck.isComplete || !isReadyForAction.value) {
      return {
        accuracy: 0,
        stage: completenessCheck.isComplete ? 'preparing' : 'incomplete',
        feedback: completenessCheck.isComplete ?
          (readyFrameCount.value < READY_FRAME_THRESHOLD ?
            `请保持姿势稳定 (${readyFrameCount.value}/${READY_FRAME_THRESHOLD})` :
            '准备开始动作') :
          completenessCheck.message,
        overall: 0,
        stability: 0,
        completeness: completenessCheck.isComplete ? 100 : 0,
        consistency: 0
      }
    }

    // 执行具体的动作检测
    switch (actionType) {
      case 'shoulder_touch':
        detectionResult = shoulderTouchDetector.detectShoulderTouch(
          keypoints, side, difficultyLevel, canvasWidth, canvasHeight
        )
        break
      case 'arm_raise':
        detectionResult = armRaiseDetector.detectArmRaise(
          keypoints, side, difficultyLevel, canvasWidth, canvasHeight
        )
        break
      case 'finger_touch':
        detectionResult = fingerTouchDetector.detectFingerTouch(
          keypoints, side, difficultyLevel, canvasWidth, canvasHeight
        )
        break
      case 'palm_flip':
        detectionResult = palmFlipDetector.detectPalmFlip(
          keypoints, side, difficultyLevel, canvasWidth, canvasHeight
        )
        break
      default:
        console.warn(`[EnhancedTrainingSession] 未知动作类型: ${actionType}`)
        return {
          accuracy: 0,
          stage: 'invalid',
          feedback: '未知动作类型',
          overall: 0,
          stability: 0,
          completeness: 0,
          consistency: 0
        }
    }

    // 计算多维度评分
    const scores = actionScoring.calculateMultiDimensionalScore(
      detectionResult,
      difficultyLevel,
      actionType
    )

    // 生成智能反馈
    const feedback = actionFeedback.generateFeedback(
      detectionResult,
      actionType,
      side,
      scores.accuracy
    )

    return {
      ...detectionResult,
      ...scores,
      feedback: feedback.text,
      feedbackType: feedback.type
    }
  }

  /**
   * 获取动作所需的关键点
   */
  const getRequiredKeypoints = (actionType, side) => {
    switch (actionType) {
      case 'shoulder_touch':
        return shoulderTouchDetector.REQUIRED_KEYPOINTS[side]
      case 'arm_raise':
        return armRaiseDetector.REQUIRED_KEYPOINTS[side]
      case 'finger_touch':
        return fingerTouchDetector.REQUIRED_KEYPOINTS[side]
      case 'palm_flip':
        return palmFlipDetector.REQUIRED_KEYPOINTS[side]
      default:
        return []
    }
  }

  /**
   * 处理检测结果
   */
  const handleDetectionResult = (detectionResult) => {
    const { overall, accuracy, stability, completeness, feedback, stage } = detectionResult

    console.log('[EnhancedTrainingSession] 检测结果:', {
      overall, accuracy, stability, completeness, feedback, stage
    })

    // 处理画面不完整情况
    if (stage === 'incomplete' || completeness < 100) {
      handleFrameIncomplete()
      return
    }

    // 处理无效检测
    if (stage === 'invalid') {
      handleInvalidDetection()
      return
    }

    // 处理准备阶段逻辑
    handleReadyPhase(detectionResult)

    // 只有在准备就绪后才进行正常的动作检测和反馈
    if (isReadyForAction.value) {
      // 处理音频反馈
      handleAudioFeedback(detectionResult)

      // 检查动作完成条件
      checkActionCompletion(detectionResult)
    }
  }

  /**
   * 处理准备阶段逻辑
   */
  const handleReadyPhase = (detectionResult) => {
    const { completeness } = detectionResult

    if (completeness >= 100) {
      // 画面完整，增加帧计数
      readyFrameCount.value++

      if (readyFrameCount.value >= READY_FRAME_THRESHOLD && !isReadyForAction.value) {
        // 达到准备条件
        isReadyForAction.value = true
        showReadyIndicator.value = true

        // 播放准备就绪提示音
        audioFeedback.playAudio('ready_beep')

        // 显示绿色框动画效果
        setTimeout(() => {
          showReadyIndicator.value = false
        }, 2000) // 2秒后隐藏绿色框

        console.log('[EnhancedTrainingSession] 准备就绪，可以开始动作')
      }
    } else {
      // 画面不完整，重置计数和状态
      readyFrameCount.value = 0
      isReadyForAction.value = false
      showReadyIndicator.value = false
    }
  }

  /**
   * 处理画面不完整
   */
  const handleFrameIncomplete = () => {
    // 重置动作计时和准备状态
    resetActionHoldTimer()
    resetReadyState()

    // 播放警告音（限制频率）
    const now = Date.now()
    if (now - lastFeedbackTime.value > 3000) {
      audioFeedback.playFrameWarningAudio()
      lastFeedbackTime.value = now
    }
  }

  /**
   * 处理无效检测
   */
  const handleInvalidDetection = () => {
    // 重置动作计时和准备状态
    resetActionHoldTimer()
    resetReadyState()

    console.warn('[EnhancedTrainingSession] 检测无效')
  }

  /**
   * 重置准备状态
   */
  const resetReadyState = () => {
    readyFrameCount.value = 0
    isReadyForAction.value = false
    showReadyIndicator.value = false
  }

  /**
   * 处理音频反馈
   */
  const handleAudioFeedback = (detectionResult) => {
    const { stage, overall } = detectionResult

    // 根据阶段播放不同的音频反馈
    if (['perfect_touch', 'perfect_height', 'perfect_touch', 'excellent'].includes(stage)) {
      audioFeedback.playSuccessAudio()
    } else if (['good_touch', 'good_height', 'good_flipping'].includes(stage)) {
      audioFeedback.playProgressAudio()
    } else if (overall >= 80) {
      // 高分时播放鼓励音效
      const now = Date.now()
      if (now - lastFeedbackTime.value > 2000) {
        audioFeedback.playEncouragementAudio()
        lastFeedbackTime.value = now
      }
    }
  }



  // 添加反馈时间控制
  const lastFeedbackTime = ref(0)

  /**
   * 检查动作完成条件
   */
  const checkActionCompletion = (detectionResult) => {
    const { overall, stage } = detectionResult

    // 使用评分系统检查是否完成
    const isCompleted = actionScoring.isActionCompleted(
      actionScoring.currentScores.value,
      currentDifficultyLevel.value,
      DETECTION_CONFIG.holdDuration
    )

    // 使用检测器的完成检查
    let detectorCompleted = false
    switch (currentActionType.value) {
      case 'shoulder_touch':
        detectorCompleted = shoulderTouchDetector.isActionCompleted(detectionResult, currentDifficultyLevel.value)
        break
      case 'arm_raise':
        detectorCompleted = armRaiseDetector.isActionCompleted(detectionResult, currentDifficultyLevel.value)
        break
      case 'finger_touch':
        detectorCompleted = fingerTouchDetector.isActionCompleted(detectionResult, currentDifficultyLevel.value)
        break
      case 'palm_flip':
        detectorCompleted = palmFlipDetector.isActionCompleted(detectionResult, currentDifficultyLevel.value)
        break
    }

    if (isCompleted && detectorCompleted && overall >= DETECTION_CONFIG.scoreThreshold) {
      if (!thresholdReached.value) {
        thresholdReached.value = true
        actionStartTime.value = Date.now()
        console.log('[EnhancedTrainingSession] 达到完成条件，开始计时')

        // 播放达标提示音
        audioFeedback.playScoreThresholdAudio(overall)

        startActionHoldTimer()
      }
    } else {
      // 未达到完成条件，重置计时
      if (thresholdReached.value) {
        thresholdReached.value = false
        resetActionHoldTimer()
      }
    }
  }

  /**
   * 开始动作保持计时
   */
  const startActionHoldTimer = () => {
    if (actionHoldTimer.value) return

    actionHoldTimer.value = setInterval(() => {
      if (!actionStartTime.value) {
        clearInterval(actionHoldTimer.value)
        actionHoldTimer.value = null
        return
      }

      actionHoldTime.value = Date.now() - actionStartTime.value

      // 检查是否达到保持时间要求
      if (actionHoldTime.value >= DETECTION_CONFIG.holdDuration) {
        handleActionCompleted()
      }
    }, 50) // 50ms更新一次进度
  }

  /**
   * 重置动作保持计时
   */
  const resetActionHoldTimer = () => {
    if (actionHoldTimer.value) {
      clearInterval(actionHoldTimer.value)
      actionHoldTimer.value = null
    }
    actionStartTime.value = null
    actionHoldTime.value = 0
    thresholdReached.value = false
  }

  /**
   * 处理动作完成
   */
  const handleActionCompleted = () => {
    if (!currentActionType.value) return

    console.log(`[EnhancedTrainingSession] 动作完成: ${currentActionType.value}`)

    // 计算最终得分
    const recentScores = actionScoring.scoreHistory.value
      .filter(record => record.actionType === currentActionType.value)
      .slice(-10)
      .map(record => record.overall)

    const finalScore = recentScores.length > 0
      ? Math.round(recentScores.reduce((sum, score) => sum + score, 0) / recentScores.length)
      : currentScores.value.overall

    // 判断是否达到标准
    const passThreshold = 75 // 通过阈值
    const isActionPassed = finalScore >= passThreshold

    if (isActionPassed) {
      // 动作完成，播放庆祝音效
      const gradeInfo = actionScoring.getScoreGrade(finalScore)
      audioFeedback.playCelebrationAudio(gradeInfo.grade, finalScore)

      // 显示完成动画和提示
      actionFeedback.generateFeedback(
        { stage: 'completed', feedback: `太棒了！动作完成，得分：${finalScore}` },
        currentActionType.value,
        currentActionSide.value,
        finalScore
      )

      // 重置动作状态
      resetActionState()

      // 触发状态转换到下一个动作
      stateTransition.handleActionComplete(finalScore)
    } else {
      // 动作未达标，提示重试
      audioFeedback.playAudio('encouragement')

      actionFeedback.generateFeedback(
        { stage: 'retry', feedback: `得分：${finalScore}，请再试一次，加油！` },
        currentActionType.value,
        currentActionSide.value,
        finalScore
      )

      // 重置动作状态，但保持在当前动作
      resetActionHoldTimer()
      resetReadyState()

      console.log(`[EnhancedTrainingSession] 动作未达标 (${finalScore}/${passThreshold})，请重试`)
    }
  }

  /**
   * 重置动作状态
   */
  const resetActionState = () => {
    resetActionHoldTimer()
    resetReadyState()
    thresholdReached.value = false
    actionScoring.resetScoring()
    actionFeedback.resetFeedback()

    // 重置检测器状态
    palmFlipDetector.resetFlipHistory()
  }

  return {
    // 响应式数据
    isTrainingActive,
    actionHoldTime,
    thresholdReached,

    // 准备阶段状态
    isReadyForAction,
    readyFrameCount,
    showReadyIndicator,

    // 计算属性
    currentActionType,
    currentActionSide,
    currentDifficultyLevel,
    isActionInProgress,
    actionCompletionProgress,
    currentScores,
    visibilityStatus,

    // 智能反馈
    currentFeedback,

    // 核心方法
    startTrainingSession,
    stopTrainingSession,
    handleActionCompleted,

    // 检测器访问
    detectionEngine,
    shoulderTouchDetector,
    armRaiseDetector,
    fingerTouchDetector,
    palmFlipDetector,

    // 反馈和评分系统
    actionFeedback,
    actionScoring,

    // 内部方法（暴露用于调试）
    resetActionState,
    performActionDetection,
    resetReadyState
  }
}
