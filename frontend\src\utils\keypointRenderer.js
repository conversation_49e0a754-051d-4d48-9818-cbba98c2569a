// utils/keypointRenderer.js
/**
 * 关键点渲染器类
 * 负责在画布上绘制姿态关键点和连接线
 */
export class KeypointRenderer {
  constructor(canvas, options = {}) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');
    this.keypoints = [];
    this.imageElement = null;
    this.transformParams = null; // 存储转换参数
    this.transformedKeypoints = []; // 存储转换后的关键点
    
    this.options = {
      pointRadius: 4,
      lineWidth: 2,
      pointColor: 'red',
      lineColor: 'lime',
      confidenceThreshold: 0.3,
      smoothingFactor: 0.4,
      connections: [],
      ...options,
    };

    this.isRendering = false;
    this.animationId = null;
    this.smoothedKeypoints = [];
  }

  /**
   * 更新关键点数据
   * @param {Array} keypoints - 归一化关键点数组
   */
  updateKeypoints(keypoints) {
    this.keypoints = keypoints || [];
    this.applySmoothing();
    this.calculateTransformedKeypoints(); // 计算转换后的坐标
  }

  /**
   * 更新图像元素
   * @param {HTMLImageElement} imageElement - 图像元素
   */
  updateImageElement(imageElement) {
    this.imageElement = imageElement;
    this.calculateTransformedKeypoints(); // 重新计算转换坐标
  }

  /**
   * 更新画布
   * @param {HTMLCanvasElement} canvas - 新的画布元素
   */
  updateCanvas(canvas) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');
    this.calculateTransformedKeypoints(); // 重新计算转换坐标
  }

  /**
   * 计算转换参数和转换后的关键点
   */
  calculateTransformedKeypoints() {
    if (!this.canvas || !this.imageElement || !this.keypoints.length) {
      this.transformParams = null;
      this.transformedKeypoints = [];
      return;
    }

    const canvasWidth = this.canvas.clientWidth;
    const canvasHeight = this.canvas.clientHeight;
    const imageNaturalWidth = this.imageElement.naturalWidth;
    const imageNaturalHeight = this.imageElement.naturalHeight;

    // 计算转换参数
    if (imageNaturalWidth === 0 || imageNaturalHeight === 0) {
      this.transformParams = null;
      this.transformedKeypoints = [];
      return;
    }

    const imageAspectRatio = imageNaturalWidth / imageNaturalHeight;
    const canvasAspectRatio = canvasWidth / canvasHeight;

    let renderWidth, renderHeight, xOffset, yOffset;

    if (imageAspectRatio > canvasAspectRatio) {
      renderHeight = canvasHeight;
      renderWidth = canvasHeight * imageAspectRatio;
      xOffset = (canvasWidth - renderWidth) / 2;
      yOffset = 0;
    } else {
      renderWidth = canvasWidth;
      renderHeight = canvasWidth / imageAspectRatio;
      xOffset = 0;
      yOffset = (canvasHeight - renderHeight) / 2;
    }

    this.transformParams = {
      renderWidth,
      renderHeight,
      xOffset,
      yOffset,
      canvasWidth,
      canvasHeight
    };

    // 转换关键点坐标
    this.transformedKeypoints = this.smoothedKeypoints.map((point) => {
      if (!point || point.length < 3 || point[2] < this.options.confidenceThreshold) {
        return null;
      }

      return {
        x: point[0] * renderWidth + xOffset,
        y: point[1] * renderHeight + yOffset,
        confidence: point[2]
      };
    });
  }

  /**
   * 获取转换后的关键点数据
   * @returns {Array} - 转换后的关键点数组
   */
  getTransformedKeypoints() {
    return this.transformedKeypoints;
  }

  /**
   * 获取转换参数
   * @returns {Object|null} - 转换参数对象
   */
  getTransformParams() {
    return this.transformParams;
  }

  /**
   * 应用平滑处理
   */
  applySmoothing() {
    if (!this.keypoints || this.keypoints.length === 0) {
      this.smoothedKeypoints = [];
      return;
    }

    if (this.smoothedKeypoints.length === 0) {
      this.smoothedKeypoints = [...this.keypoints];
      return;
    }

    const factor = this.options.smoothingFactor;
    this.smoothedKeypoints = this.keypoints.map((point, index) => {
      const prevPoint = this.smoothedKeypoints[index];
      
      if (!point || point.length < 3 || point[2] < this.options.confidenceThreshold) {
        return prevPoint || point;
      }
      
      if (!prevPoint || prevPoint.length < 3) {
        return point;
      }

      return [
        prevPoint[0] * factor + point[0] * (1 - factor),
        prevPoint[1] * factor + point[1] * (1 - factor),
        point[2]
      ];
    });
  }

  /**
   * 绘制关键点和连接线
   */
  draw() {
    if (!this.ctx || !this.transformedKeypoints.length) return;

    // 获取实际的Canvas尺寸（考虑设备像素比）
    const canvasWidth = this.canvas.width;
    const canvasHeight = this.canvas.height;
    
    // 完全清除画布
    this.ctx.save();
    this.ctx.setTransform(1, 0, 0, 1, 0, 0);
    this.ctx.clearRect(0, 0, canvasWidth, canvasHeight);
    this.ctx.restore();

    // 绘制连接线
    this.drawConnections();
    
    // 绘制关键点
    this.drawKeypoints();
  }

  /**
   * 绘制连接线
   */
  drawConnections() {
    this.ctx.strokeStyle = this.options.lineColor;
    this.ctx.lineWidth = this.options.lineWidth;
    this.ctx.lineCap = 'round';
    this.ctx.lineJoin = 'round';

    this.ctx.beginPath();
    
    for (const [startIdx, endIdx] of this.options.connections) {
      const startPoint = this.transformedKeypoints[startIdx];
      const endPoint = this.transformedKeypoints[endIdx];

      if (startPoint && endPoint && 
          startPoint.confidence > this.options.confidenceThreshold &&
          endPoint.confidence > this.options.confidenceThreshold) {
        this.ctx.moveTo(startPoint.x, startPoint.y);
        this.ctx.lineTo(endPoint.x, endPoint.y);
      }
    }
    
    this.ctx.stroke();
  }

  /**
   * 绘制关键点
   */
  drawKeypoints() {
    this.ctx.fillStyle = this.options.pointColor;

    for (const point of this.transformedKeypoints) {
      if (point && point.confidence > this.options.confidenceThreshold) {
        this.ctx.beginPath();
        this.ctx.arc(point.x, point.y, this.options.pointRadius, 0, 2 * Math.PI);
        this.ctx.fill();
      }
    }
  }

  /**
   * 开始渲染循环
   */
  startRenderLoop() {
    if (this.isRendering) return;
    
    this.isRendering = true;
    const renderFrame = () => {
      if (!this.isRendering) return;
      
      this.draw();
      this.animationId = requestAnimationFrame(renderFrame);
    };
    
    renderFrame();
  }

  /**
   * 停止渲染循环
   */
  stopRenderLoop() {
    this.isRendering = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  /**
   * 销毁渲染器
   */
  destroy() {
    this.stopRenderLoop();
    this.keypoints = [];
    this.transformedKeypoints = [];
    this.smoothedKeypoints = [];
    this.transformParams = null;
    this.imageElement = null;
  }
}


