/**
 * 坐标转换工具类
 * 提供归一化坐标与画布坐标之间的转换
 */
export class CoordinateTransformer {
  /**
   * 计算坐标转换参数
   * @param {number} canvasWidth - 画布宽度
   * @param {number} canvasHeight - 画布高度
   * @param {number} imageNaturalWidth - 图像原始宽度
   * @param {number} imageNaturalHeight - 图像原始高度
   * @returns {Object} - 转换参数 {renderWidth, renderHeight, xOffset, yOffset}
   */
  static calculateTransformParams(canvasWidth, canvasHeight, imageNaturalWidth, imageNaturalHeight) {
    if (imageNaturalWidth === 0 || imageNaturalHeight === 0) {
      return null
    }

    const imageAspectRatio = imageNaturalWidth / imageNaturalHeight
    const canvasAspectRatio = canvasWidth / canvasHeight

    let renderWidth, renderHeight, xOffset, yOffset

    if (imageAspectRatio > canvasAspectRatio) {
      renderHeight = canvasHeight
      renderWidth = canvasHeight * imageAspectRatio
      xOffset = (canvasWidth - renderWidth) / 2
      yOffset = 0
    } else {
      renderWidth = canvasWidth
      renderHeight = canvasWidth / imageAspectRatio
      xOffset = 0
      yOffset = (canvasHeight - renderHeight) / 2
    }

    return {
      renderWidth,
      renderHeight,
      xOffset,
      yOffset
    }
  }

  /**
   * 将归一化坐标转换为画布坐标
   * @param {Array} normalizedKeypoints - 归一化关键点数组 [[x, y, confidence], ...]
   * @param {Object} transformParams - 转换参数
   * @param {number} confidenceThreshold - 置信度阈值
   * @returns {Array} - 画布坐标关键点数组
   */
  static transformToCanvas(normalizedKeypoints, transformParams, confidenceThreshold = 0.3) {
    if (!transformParams || !normalizedKeypoints) {
      return []
    }

    const { renderWidth, renderHeight, xOffset, yOffset } = transformParams

    return normalizedKeypoints.map((point) => {
      if (!point || point.length < 3 || point[2] < confidenceThreshold) {
        return null
      }

      return {
        x: point[0] * renderWidth + xOffset,
        y: point[1] * renderHeight + yOffset,
        confidence: point[2]
      }
    })
  }

  /**
   * 检查归一化坐标是否在有效范围内
   * @param {Object} normalizedPoint - 归一化坐标点 {x, y, confidence}
   * @returns {boolean} - 是否在有效范围内
   */
  static isNormalizedPointValid(normalizedPoint) {
    if (!normalizedPoint) return false
    
    // 检查坐标是否为无效值
    if (normalizedPoint.x === 0 && normalizedPoint.y === 0) return false
    if (normalizedPoint.x === undefined || normalizedPoint.y === undefined) return false
    
    // 检查是否在归一化范围内 (0-1)
    if (normalizedPoint.x < 0 || normalizedPoint.x > 1) return false
    if (normalizedPoint.y < 0 || normalizedPoint.y > 1) return false
    
    // 检查置信度
    if (normalizedPoint.confidence !== undefined && normalizedPoint.confidence < 0.3) return false
    
    return true
  }

  /**
   * 检查画布坐标是否在边界内
   * @param {Object} canvasPoint - 画布坐标点 {x, y, confidence}
   * @param {number} canvasWidth - 画布宽度
   * @param {number} canvasHeight - 画布高度
   * @param {number} margin - 边界容忍度
   * @returns {boolean} - 是否在边界内
   */
  static isCanvasPointInBounds(canvasPoint, canvasWidth, canvasHeight, margin = 20) {
    if (!canvasPoint) return false
    
    return canvasPoint.x >= margin && 
           canvasPoint.x <= canvasWidth - margin && 
           canvasPoint.y >= margin && 
           canvasPoint.y <= canvasHeight - margin
  }
}